<header class="modern-header">
    <div class="header-container">
        <div class="header-content">
            <!-- Logo Section -->
            <div class="header-logo-section">
                <a href="/" class="header-logo">
                    <img src="/WhaMart_Logo.png" alt="Whamart Logo" class="header-logo-img" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<span class=\'header-logo-fallback\'>W</span>');">
                </a>
            </div>

            <!-- Navigation -->
            <nav class="header-nav">
                <a href="#features" class="nav-link">Features</a>
                <a href="#solutions" class="nav-link">Solutions</a>
                <a href="#pricing" class="nav-link">Pricing</a>
                <a href="#testimonials" class="nav-link">Testimonials</a>
                <a href="#contact" class="nav-link">Contact</a>
            </nav>

            <!-- Header Buttons -->
            <div class="header-actions">
                <a href="/login" class="btn-signin">Sign In</a>
                <a href="/register" class="btn-signup">Get Started</a>
            </div>

            <!-- Mobile Menu Button -->
            <div class="mobile-menu-toggle">
                <button id="mobile-menu-button" class="menu-button" aria-label="Toggle menu">
                    <span class="menu-icon">
                        <span></span>
                        <span></span>
                        <span></span>
                    </span>
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mobile-menu hidden">
        <div class="mobile-menu-content">
            <a href="#features" class="mobile-nav-link">Features</a>
            <a href="#solutions" class="mobile-nav-link">Solutions</a>
            <a href="#pricing" class="mobile-nav-link">Pricing</a>
            <a href="#testimonials" class="mobile-nav-link">Testimonials</a>
            <a href="#contact" class="mobile-nav-link">Contact</a>
            <div class="mobile-actions">
                <a href="/login" class="mobile-btn-signin">Sign In</a>
                <a href="/register" class="mobile-btn-signup">Get Started</a>
            </div>
        </div>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const menuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            if (menuButton && mobileMenu) {
                menuButton.addEventListener('click', function () {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>
</header>
