<header class="whatsapp-header">
    <div class="container header-content">
        <div class="logo-container flex items-center">
            <a href="/">
                <img src="/img/WhaMart_Logo.png" alt="Whamart Logo" class="logo-img" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<span class=\'logo-fallback\'>W</span>');">
            </a>
            <span class="logo-text ml-2">Whamart</span>
        </div>
        <nav class="main-nav">
            <a href="#features">Features</a>
            <a href="#how-it-works">How It Works</a>
            <a href="#pricing">Pricing</a>
            <a href="#testimonials">Testimonials</a>
            <a href="#faq">FAQ</a>
        </nav>
        <div class="header-buttons">
            <a href="/login" class="btn btn-header-signin">Login</a>
            <a href="/register" class="btn btn-header-signup">Start Free Trial</a>
        </div>
        <div class="mobile-menu-button-container">
            <button id="mobile-menu-button"><i class="fas fa-bars"></i></button>
        </div>
    </div>
    <div id="mobile-menu" class="mobile-menu hidden">
        <a href="#features">Features</a>
        <a href="#how-it-works">How It Works</a>
        <a href="#pricing">Pricing</a>
        <a href="#testimonials">Testimonials</a>
        <a href="#faq">FAQ</a>
        <a href="/login">Login</a>
        <a href="/register" class="btn btn-header-signup" style="margin-top:10px;">Start Free Trial</a>
    </div>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const menuButton = document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            if (menuButton && mobileMenu) {
                menuButton.addEventListener('click', function () {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>
</header>
