@extends('layouts.app')

@section('content')
<!-- Hero Section -->
<section class="modern-hero-section">
    <div class="hero-container">
        <div class="hero-content">
            <div class="hero-text">
                <div class="hero-badge">
                    <span class="badge-icon">🚀</span>
                    <span>India's #1 Store Builder</span>
                </div>
                <h1 class="hero-title">
                    Build Your Modern Online Store in
                    <span class="highlight-text">Minutes</span>
                    <div class="title-underline"></div>
                </h1>
                <p class="hero-description">
                    Create stunning, mobile-first stores with chat-style interface. Accept payments,
                    manage orders, and grow your business online with zero coding required.
                </p>
                <div class="hero-actions">
                    <a href="/register" class="btn-hero-primary">
                        <span>Start Free Trial</span>
                        <i class="fas fa-arrow-right"></i>
                    </a>
                    <a href="#demo" class="btn-hero-secondary">
                        <i class="fas fa-play"></i>
                        <span>Watch Demo</span>
                    </a>
                </div>
                <div class="hero-trust">
                    <span class="trust-text">Trusted by</span>
                    <div class="trust-logos">
                        <div class="trust-item">
                            <i class="fas fa-store"></i>
                            <span>Local Shops</span>
                        </div>
                        <div class="trust-item">
                            <i class="fas fa-tshirt"></i>
                            <span>Fashion Brands</span>
                        </div>
                        <div class="trust-item">
                            <i class="fas fa-utensils"></i>
                            <span>Restaurants</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="hero-visual">
                <div class="hero-image-container">
                    <div class="floating-card card-1">
                        <i class="fas fa-shopping-cart"></i>
                        <span>Easy Orders</span>
                    </div>
                    <div class="floating-card card-2">
                        <i class="fas fa-rupee-sign"></i>
                        <span>Instant Payments</span>
                    </div>
                    <div class="floating-card card-3">
                        <i class="fas fa-chart-line"></i>
                        <span>Growth Analytics</span>
                    </div>
                    <div class="hero-main-visual">
                        <div class="phone-mockup">
                            <div class="phone-screen">
                                <div class="chat-interface">
                                    <div class="chat-header">
                                        <div class="store-avatar"></div>
                                        <span>Your Store</span>
                                    </div>
                                    <div class="chat-messages">
                                        <div class="message-bubble">Welcome to our store! 👋</div>
                                        <div class="message-bubble user">Show me products</div>
                                        <div class="message-bubble">Here are our latest items...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="hero-background-elements">
        <div class="bg-circle circle-1"></div>
        <div class="bg-circle circle-2"></div>
        <div class="bg-circle circle-3"></div>
    </div>
</section>



<!-- Problem & Solution Section -->
<section class="problem-solution-section section-light-green" id="problem">
    <div class="container">
        <div class="section-header">
            <div class="section-tag">THE CHALLENGE</div>
            <h2 class="section-title">
                Why Small Businesses Struggle
                <span class="highlight-text">Online</span>
                <div class="title-underline"></div>
            </h2>
            <p class="section-subtitle">
                Complex platforms, high costs, and technical barriers prevent local entrepreneurs from going digital
            </p>
        </div>
        <div class="problem-solution-grid">
            <div class="problem-solution-card">
                <div class="card-icon problem-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-title">Complex Setup</h3>
                    <p class="card-description">Traditional e-commerce platforms require technical knowledge and weeks of setup</p>
                </div>
                <div class="solution-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
                <div class="card-icon solution-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-title">10-Minute Setup</h3>
                    <p class="card-description">Launch your store in minutes with our intuitive, no-code platform</p>
                </div>
            </div>

            <div class="problem-solution-card">
                <div class="card-icon problem-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-title">High Commissions</h3>
                    <p class="card-description">Marketplace platforms charge 15-30% commission on every sale</p>
                </div>
                <div class="solution-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
                <div class="card-icon solution-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-title">Zero Commission</h3>
                    <p class="card-description">Keep 100% of your earnings with direct UPI payments</p>
                </div>
            </div>

            <div class="problem-solution-card">
                <div class="card-icon problem-icon">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-title">Poor Mobile Experience</h3>
                    <p class="card-description">Most platforms aren't optimized for mobile-first Indian customers</p>
                </div>
                <div class="solution-arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>
                <div class="card-icon solution-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="card-content">
                    <h3 class="card-title">Chat-Style Interface</h3>
                    <p class="card-description">Familiar messaging experience that customers love and trust</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Features Section -->
<section class="features-section section-white" id="features">
    <div class="container">
        <div class="section-header">
            <div class="section-tag">POWERFUL FEATURES</div>
            <h2 class="section-title">
                Complete Online Store
                <span class="highlight-text">Solution</span>
                <div class="title-underline"></div>
            </h2>
            <p class="section-subtitle">Everything you need to create, manage, and grow your online business</p>
        </div>
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Digital Storefront</h3>
                    <p class="feature-description">Launch a beautiful, mobile-optimized store in minutes. No coding required.</p>
                    <div class="feature-highlight">
                        <i class="fas fa-check"></i>
                        <span>Drag & Drop Builder</span>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-comments"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Chat-Style Interface</h3>
                    <p class="feature-description">Familiar messaging experience that customers love and trust.</p>
                    <div class="feature-highlight">
                        <i class="fas fa-check"></i>
                        <span>WhatsApp-Style UI</span>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-rupee-sign"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Direct UPI Payments</h3>
                    <p class="feature-description">Get paid instantly with zero gateway fees. Money goes straight to your account.</p>
                    <div class="feature-highlight">
                        <i class="fas fa-check"></i>
                        <span>0% Commission</span>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-file-invoice"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Smart Invoicing</h3>
                    <p class="feature-description">Generate professional PDF bills automatically for every order.</p>
                    <div class="feature-highlight">
                        <i class="fas fa-check"></i>
                        <span>Auto-Generated</span>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Analytics Dashboard</h3>
                    <p class="feature-description">Track sales, customers, and growth with detailed insights.</p>
                    <div class="feature-highlight">
                        <i class="fas fa-check"></i>
                        <span>Real-time Data</span>
                    </div>
                </div>
            </div>

            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-shield-check"></i>
                </div>
                <div class="feature-content">
                    <h3 class="feature-title">Verified Business</h3>
                    <p class="feature-description">Build instant trust with verified business badge and secure payments.</p>
                    <div class="feature-highlight">
                        <i class="fas fa-check"></i>
                        <span>Blue Tick Verified</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Comparison Section -->
<section class="comparison-section section-light-green" id="comparison">
    <div class="container">
        <div class="section-header">
            <div class="section-tag">COMPARISON</div>
            <h2 class="section-title">
                Why Choose Whamart Over
                <span class="highlight-text">Others</span>
                <div class="title-underline"></div>
            </h2>
            <p class="section-subtitle">See how we compare against traditional solutions</p>
        </div>
        <div class="comparison-table">
            <div class="comparison-header">
                <div class="comparison-feature">Features</div>
                <div class="comparison-option">
                    <div class="option-logo">
                        <i class="fas fa-shopping-bag"></i>
                    </div>
                    <div class="option-name">Dukaan</div>
                </div>
                <div class="comparison-option">
                    <div class="option-logo">
                        <i class="fas fa-desktop"></i>
                    </div>
                    <div class="option-name">Digital Showroom</div>
                </div>
                <div class="comparison-option">
                    <div class="option-logo">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="option-name">WhatsApp Business API</div>
                </div>
                <div class="comparison-option highlight">
                    <div class="option-logo">
                        <i class="fas fa-crown"></i>
                    </div>
                    <div class="option-name">Whamart</div>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Setup Time</div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>30-60 Minutes</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>2-3 Days</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>1-2 Weeks</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>10 Minutes</span>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Monthly Cost</div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>₹999/month</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>₹2,999/month</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>₹5,000+/month</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>₹125/month</span>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Transaction Fees</div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>2% + Gateway</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>2.5% + Gateway</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>Per Message Cost</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>0% Commission</span>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Chat Interface</div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>No Chat UI</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>Basic Chat</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>WhatsApp Only</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>WhatsApp-Style UI</span>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Payment Options</div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>Gateway Required</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>Limited Options</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>Complex Integration</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>Direct UPI + All</span>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Customization</div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>Limited Themes</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-check text-green"></i>
                    <span>Good Customization</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>Technical Knowledge</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>Full Customization</span>
                </div>
            </div>

            <div class="comparison-row">
                <div class="comparison-feature">Customer Support</div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>Email Support</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-minus text-yellow"></i>
                    <span>Chat Support</span>
                </div>
                <div class="comparison-value">
                    <i class="fas fa-times text-red"></i>
                    <span>Developer Support</span>
                </div>
                <div class="comparison-value highlight">
                    <i class="fas fa-check text-green"></i>
                    <span>24/7 WhatsApp Support</span>
                </div>
            </div>
        </div>

    </div>
</section>

<!-- Pricing Section -->
<section class="pricing-section section-white" id="pricing">
    <div class="container">
        <div class="section-header">
            <div class="section-tag">AFFORDABLE PRICING</div>
            <h2 class="section-title">
                Simple, Transparent
                <span class="highlight-text">Pricing</span>
                <div class="title-underline"></div>
            </h2>
            <p class="section-subtitle">No hidden fees. No commissions. Choose your plan and start selling today.</p>
        </div>
        <div class="pricing-grid">
            <div class="pricing-card">
                <div class="pricing-header">
                    <h3 class="plan-name">Starter</h3>
                    <div class="plan-price">
                        <span class="currency">₹</span>
                        <span class="amount">125</span>
                        <span class="period">/month</span>
                    </div>
                    <div class="plan-description">Perfect for small businesses</div>
                </div>
                <div class="pricing-features">
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Up to 100 products</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Chat-style interface</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Direct UPI payments</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Basic analytics</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Email support</span>
                    </div>
                </div>
                <div class="pricing-footer">
                    <a href="/register?plan=starter" class="pricing-btn">Get Started</a>
                </div>
            </div>

            <div class="pricing-card popular">
                <div class="popular-badge">Most Popular</div>
                <div class="pricing-header">
                    <h3 class="plan-name">Business</h3>
                    <div class="plan-price">
                        <span class="currency">₹</span>
                        <span class="amount">250</span>
                        <span class="period">/month</span>
                    </div>
                    <div class="plan-description">Best for growing businesses</div>
                </div>
                <div class="pricing-features">
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Up to 1000 products</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Advanced automation</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>All payment methods</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Advanced analytics</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Priority support</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Verified business badge</span>
                    </div>
                </div>
                <div class="pricing-footer">
                    <a href="/register?plan=business" class="pricing-btn">Get Started</a>
                </div>
            </div>

            <div class="pricing-card">
                <div class="pricing-header">
                    <h3 class="plan-name">Enterprise</h3>
                    <div class="plan-price">
                        <span class="amount">Custom</span>
                    </div>
                    <div class="plan-description">For large businesses</div>
                </div>
                <div class="pricing-features">
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Unlimited products</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Custom automation</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>White-label solution</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Dedicated manager</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>24/7 phone support</span>
                    </div>
                    <div class="feature-item">
                        <i class="fas fa-check"></i>
                        <span>Custom integrations</span>
                    </div>
                </div>
                <div class="pricing-footer">
                    <a href="/contact-sales" class="pricing-btn secondary">Contact Sales</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section section-light-green" id="testimonials">
    <div class="container">
        <div class="section-header">
            <div class="section-tag">SUCCESS STORIES</div>
            <h2 class="section-title">
                What Our Customers
                <span class="highlight-text">Say</span>
                <div class="title-underline"></div>
            </h2>
            <p class="section-subtitle">Real stories from real businesses growing with Whamart</p>
        </div>
        <div class="testimonials-slider">
            <div class="testimonials-track">
                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-content">
                        "Whamart transformed my home bakery into a thriving online business. Setup was incredibly easy!"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/api/placeholder/50/50" alt="Priya Sharma" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'avatar-fallback\'>PS</div>');">
                        </div>
                        <div class="author-info">
                            <div class="author-name">Priya Sharma</div>
                            <div class="author-business">Sweet Dreams Bakery, Mumbai</div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-content">
                        "Best decision for my electronics store. Sales increased by 300% in just 2 months!"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/api/placeholder/50/50" alt="Rahul Mehta" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'avatar-fallback\'>RM</div>');">
                        </div>
                        <div class="author-info">
                            <div class="author-name">Rahul Mehta</div>
                            <div class="author-business">TechZone Electronics, Delhi</div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-content">
                        "The chat interface is exactly what my customers wanted. Orders doubled overnight!"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/api/placeholder/50/50" alt="Ananya Patel" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'avatar-fallback\'>AP</div>');">
                        </div>
                        <div class="author-info">
                            <div class="author-name">Ananya Patel</div>
                            <div class="author-business">Fashion Forward, Bangalore</div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-content">
                        "Zero commission policy is a game-changer. Finally keeping 100% of my profits!"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/api/placeholder/50/50" alt="Vikram Singh" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'avatar-fallback\'>VS</div>');">
                        </div>
                        <div class="author-info">
                            <div class="author-name">Vikram Singh</div>
                            <div class="author-business">Organic Farms, Punjab</div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-content">
                        "Customer support is outstanding. They helped me set up everything in minutes!"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/api/placeholder/50/50" alt="Meera Joshi" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'avatar-fallback\'>MJ</div>');">
                        </div>
                        <div class="author-info">
                            <div class="author-name">Meera Joshi</div>
                            <div class="author-business">Handmade Crafts, Jaipur</div>
                        </div>
                    </div>
                </div>

                <div class="testimonial-card">
                    <div class="testimonial-rating">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                    </div>
                    <div class="testimonial-content">
                        "Perfect for my restaurant. Online orders are now 60% of my total business!"
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/api/placeholder/50/50" alt="Arjun Kumar" onerror="this.style.display='none';this.parentNode.insertAdjacentHTML('beforeend', '<div class=\'avatar-fallback\'>AK</div>');">
                        </div>
                        <div class="author-info">
                            <div class="author-name">Arjun Kumar</div>
                            <div class="author-business">Spice Garden Restaurant, Chennai</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="testimonials-stats">
            <div class="stat-item">
                <div class="stat-number">4.9/5</div>
                <div class="stat-label">Average Rating</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">1000+</div>
                <div class="stat-label">Happy Customers</div>
            </div>
            <div class="stat-item">
                <div class="stat-number">24/7</div>
                <div class="stat-label">Support Available</div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="faq-section section-white" id="faq">
    <div class="container">
        <div class="section-heading">
            <span class="section-tag">FAQ</span>
            <h2>Frequently Asked Questions</h2>
        </div>
        <div class="faq-container">
            <div class="faq-item">
                <button class="faq-question">Do I need coding skills to use Whamart? <i class="fas fa-chevron-down"></i></button>
                <div class="faq-answer">
                    <p>No coding skills are needed! Our platform is designed to be user-friendly with a simple drag-and-drop interface that anyone can use to set up their online store.</p>
                </div>
            </div>
            <div class="faq-item">
                <button class="faq-question">How do I receive payments? <i class="fas fa-chevron-down"></i></button>
                <div class="faq-answer">
                    <p>You'll receive payments directly to your bank account via UPI. We don't take any commission on your sales - you keep 100% of your earnings.</p>
                </div>
            </div>
            <div class="faq-item">
                <button class="faq-question">Can I try before I buy? <i class="fas fa-chevron-down"></i></button>
                <div class="faq-answer">
                    <p>Yes! We offer a 14-day free trial with no credit card required. You can explore all features and decide if Whamart is right for your business.</p>
                </div>
            </div>
            <div class="faq-item">
                <button class="faq-question">What kind of support do you offer? <i class="fas fa-chevron-down"></i></button>
                <div class="faq-answer">
                    <p>We provide email and chat support with a 24-hour response time. Business and Enterprise plans come with priority support and faster response times.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Final CTA Section -->
<section class="cta-section section-light-green global-cta">
    <div class="container cta-content">
        <h2>Ready to Launch Your Online Store?</h2>
        <p>Join 10,000+ businesses using Whamart to create beautiful online stores. Start your free trial today!</p>
        <div class="cta-buttons">
            <a href="/register" class="btn btn-primary">Start Free Trial</a>
            <a href="#demo" class="btn btn-outline">Watch Demo</a>
        </div>
    </div>
</section>

@push('scripts')
<script>
// FAQ Accordion
const faqItems = document.querySelectorAll('.faq-item');
faqItems.forEach(item => {
    const question = item.querySelector('.faq-question');
    question.addEventListener('click', () => {
        const isActive = item.classList.contains('active');
        // Close all items
        faqItems.forEach(i => {
            i.classList.remove('active');
            i.querySelector('.faq-answer').style.maxHeight = 0;
            i.querySelector('.fa-chevron-down').style.transform = 'rotate(0deg)';
        });
        // Open clicked item if it was closed
        if (!isActive) {
            item.classList.add('active');
            const answer = item.querySelector('.faq-answer');
            answer.style.maxHeight = answer.scrollHeight + 'px';
            item.querySelector('.fa-chevron-down').style.transform = 'rotate(180deg)';
        }
    });
});
// Initialize first FAQ as open by default
window.addEventListener('DOMContentLoaded', () => {
    const firstFaq = document.querySelector('.faq-item');
    if (firstFaq) {
        firstFaq.classList.add('active');
        const answer = firstFaq.querySelector('.faq-answer');
        answer.style.maxHeight = answer.scrollHeight + 'px';
        firstFaq.querySelector('.fa-chevron-down').style.transform = 'rotate(180deg)';
    }
});
</script>
@endpush
@endsection
